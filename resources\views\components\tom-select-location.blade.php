@props([
    'name' => 'location',
    'countryValue' => '',
    'stateValue' => '',
    'cityValue' => '',
    'required' => false,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'style' => 'bootstrap' // bootstrap or tailwind
])

@php
    $baseClasses = $style === 'tailwind' 
        ? 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        : 'form-control';
    
    $labelClasses = $style === 'tailwind'
        ? 'block text-sm font-medium text-gray-700 mb-2'
        : 'form-label';
        
    $containerClasses = $style === 'tailwind'
        ? 'grid md:grid-cols-3 gap-6'
        : 'row';
        
    $colClasses = $style === 'tailwind'
        ? ''
        : 'col-md-4 mb-3';
@endphp

<div class="{{ $containerClasses }}" data-location-container="{{ $name }}">
    @if($showCountry)
    <div class="{{ $colClasses }}">
        <label for="{{ $name }}_country" class="{{ $labelClasses }}">
            Country {{ $required ? '*' : '' }}
        </label>
        <select id="{{ $name }}_country" 
                name="country" 
                class="{{ $baseClasses }}"
                {{ $required ? 'required' : '' }}
                data-location-type="country"
                data-location-name="{{ $name }}">
            <option value="">Select Country</option>
        </select>
    </div>
    @endif
    
    @if($showState)
    <div class="{{ $colClasses }}">
        <label for="{{ $name }}_state" class="{{ $labelClasses }}">
            State/Province {{ $required ? '*' : '' }}
        </label>
        <select id="{{ $name }}_state" 
                name="state" 
                class="{{ $baseClasses }}"
                {{ $required ? 'required' : '' }}
                data-location-type="state"
                data-location-name="{{ $name }}"
                disabled>
            <option value="">Select State/Province</option>
        </select>
    </div>
    @endif
    
    @if($showCity)
    <div class="{{ $colClasses }}">
        <label for="{{ $name }}_city" class="{{ $labelClasses }}">
            City {{ $required ? '*' : '' }}
        </label>
        <select id="{{ $name }}_city" 
                name="city" 
                class="{{ $baseClasses }}"
                {{ $required ? 'required' : '' }}
                data-location-type="city"
                data-location-name="{{ $name }}"
                disabled>
            <option value="">Select City</option>
        </select>
    </div>
    @endif
</div>

@once
@push('styles')
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet">
<style>
.ts-wrapper.single .ts-control:after {
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900; 
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    position: absolute;
    right: 10px;
    pointer-events: none;
}

.ts-control {
    min-height: 44px;
    align-items: center;
    padding: 4px 8px;
    outline: 0;
}

.ts-wrapper.focus .ts-control {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.ts-dropdown {
    border: 1px solid #0d6efd;
    border-radius: 3px;
}

.ts-dropdown:not(.multi) .active {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('[data-location-container="{{ $name }}"]');
    if (!container) return;
    
    const countrySelect = container.querySelector('[data-location-type="country"]');
    const stateSelect = container.querySelector('[data-location-type="state"]');
    const citySelect = container.querySelector('[data-location-type="city"]');
    
    let countryTomSelect, stateTomSelect, cityTomSelect;
    
    // Initialize country dropdown
    if (countrySelect) {
        countryTomSelect = new TomSelect(countrySelect, {
            placeholder: 'Select Country',
            searchField: ['text'],
            valueField: 'value',
            labelField: 'text',
            load: function(query, callback) {
                if (!query.length) {
                    // Load all countries initially
                    fetch('/api/locations/countries')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const options = data.data.map(country => ({
                                    value: country.name,
                                    text: country.name
                                }));
                                callback(options);
                            } else {
                                callback();
                            }
                        })
                        .catch(() => callback());
                } else {
                    // Search countries
                    const options = Array.from(countrySelect.options)
                        .filter(option => option.text.toLowerCase().includes(query.toLowerCase()))
                        .map(option => ({
                            value: option.value,
                            text: option.text
                        }));
                    callback(options);
                }
            },
            onChange: function(value) {
                // Reset and disable dependent dropdowns
                if (stateTomSelect) {
                    stateTomSelect.clear();
                    stateTomSelect.clearOptions();
                    stateTomSelect.disable();
                }
                if (cityTomSelect) {
                    cityTomSelect.clear();
                    cityTomSelect.clearOptions();
                    cityTomSelect.disable();
                }

                if (value && stateSelect) {
                    // Find the country ID by name
                    fetch('/api/locations/countries')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const country = data.data.find(c => c.name === value);
                                if (country) {
                                    loadStates(country.id);
                                }
                            }
                        })
                        .catch(console.error);
                }
            }
        });
        
        // Load initial countries
        countryTomSelect.load('');
    }
    
    // Initialize state dropdown
    if (stateSelect) {
        stateTomSelect = new TomSelect(stateSelect, {
            placeholder: 'Select State/Province',
            searchField: ['text'],
            valueField: 'value',
            labelField: 'text',
            onChange: function(value) {
                // Reset and disable city dropdown
                if (cityTomSelect) {
                    cityTomSelect.clear();
                    cityTomSelect.clearOptions();
                    cityTomSelect.disable();
                }

                if (value && citySelect) {
                    // Find the state ID by name and country
                    const countryName = countryTomSelect ? countryTomSelect.getValue() : '';
                    if (countryName) {
                        fetch('/api/locations/countries')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const country = data.data.find(c => c.name === countryName);
                                    if (country) {
                                        return fetch(`/api/locations/countries/${country.id}/states`);
                                    }
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const state = data.data.find(s => s.name === value);
                                    if (state) {
                                        loadCities(state.id);
                                    }
                                }
                            })
                            .catch(console.error);
                    }
                }
            }
        });
    }
    
    // Initialize city dropdown
    if (citySelect) {
        cityTomSelect = new TomSelect(citySelect, {
            placeholder: 'Select City',
            searchField: ['text'],
            valueField: 'value',
            labelField: 'text'
        });
    }
    
    function loadStates(countryId) {
        if (!stateTomSelect) return;
        
        fetch(`/api/locations/countries/${countryId}/states`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    stateTomSelect.clearOptions();
                    data.data.forEach(state => {
                        stateTomSelect.addOption({
                            value: state.name,
                            text: state.name,
                            stateId: state.id
                        });
                    });
                    stateTomSelect.enable();
                }
            })
            .catch(console.error);
    }
    
    function loadCities(stateId) {
        if (!cityTomSelect) return;
        
        fetch(`/api/locations/states/${stateId}/cities`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cityTomSelect.clearOptions();
                    data.data.forEach(city => {
                        cityTomSelect.addOption({
                            value: city.name,
                            text: city.name
                        });
                    });
                    cityTomSelect.enable();
                }
            })
            .catch(console.error);
    }
    
    // Set initial values if provided
    @if($countryValue)
        if (countryTomSelect) {
            setTimeout(() => countryTomSelect.setValue('{{ $countryValue }}'), 100);
        }
    @endif
    
    @if($stateValue)
        if (stateTomSelect) {
            setTimeout(() => stateTomSelect.setValue('{{ $stateValue }}'), 200);
        }
    @endif
    
    @if($cityValue)
        if (cityTomSelect) {
            setTimeout(() => cityTomSelect.setValue('{{ $cityValue }}'), 300);
        }
    @endif
});
</script>
@endpush
@endonce
