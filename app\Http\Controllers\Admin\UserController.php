<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Mail\UserWelcome;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of all users
     */
    public function index(Request $request): View
    {
        $query = User::query();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $users = $query->latest()->paginate(20);

        // User statistics
        $stats = [
            'total_users' => User::count(),
            'admin_users' => User::where('role', 'admin')->count(),
            'staff_users' => User::where('role', 'staff')->count(),
            'customer_users' => User::where('role', 'customer')->count(),
            'active_users' => User::where('is_active', true)->count(),
            'inactive_users' => User::where('is_active', false)->count(),
        ];

        return view('admin.users.index', compact('users', 'stats'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create(): View
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'role' => ['required', Rule::in(['admin', 'staff', 'customer'])],
            'address' => 'nullable|string|max:500',
            'location.country' => 'nullable|integer|exists:countries,id',
            'location.state' => 'nullable|integer|exists:states,id',
            'location.city' => 'nullable|integer|exists:cities,id',
            'postal_code' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
        ]);

        // Store the plain password for email before hashing
        $plainPassword = $validated['password'];
        $validated['password'] = Hash::make($validated['password']);
        $validated['is_active'] = (bool) $request->input('is_active', false);

        // Get location names from IDs
        $countryName = null;
        $stateName = null;
        $cityName = null;

        if (!empty($validated['location']['country'])) {
            $country = \App\Models\Country::find($validated['location']['country']);
            $countryName = $country ? $country->name : null;
        }

        if (!empty($validated['location']['state'])) {
            $state = \App\Models\State::find($validated['location']['state']);
            $stateName = $state ? $state->name : null;
        }

        if (!empty($validated['location']['city'])) {
            $city = \App\Models\City::find($validated['location']['city']);
            $cityName = $city ? $city->name : null;
        }

        // Remove location array and add individual fields
        unset($validated['location']);
        $validated['country'] = $countryName;
        $validated['state'] = $stateName;
        $validated['city'] = $cityName;

        $user = User::create($validated);

        // Send welcome email with login credentials
        try {
            Mail::to($user->email)->send(new UserWelcome($user, $plainPassword));
        } catch (\Exception $e) {
            \Log::error('Failed to send welcome email to user: ' . $e->getMessage());
        }

        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User created successfully and welcome email sent.');
    }

    /**
     * Display the specified user
     */
    public function show(User $user): View
    {
        $user->load(['orders', 'parcels']);
        
        // Get user statistics
        $userStats = [
            'total_orders' => $user->orders()->count(),
            'total_parcels' => $user->parcels()->count(),
            'total_spent' => $user->orders()->where('payment_status', 'paid')->sum('total_amount'),
            'last_login' => $user->last_login_at,
        ];

        return view('admin.users.show', compact('user', 'userStats'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user): View
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
            'role' => ['required', Rule::in(['admin', 'staff', 'customer'])],
            'address' => 'nullable|string|max:500',
            'location.country' => 'nullable|integer|exists:countries,id',
            'location.state' => 'nullable|integer|exists:states,id',
            'location.city' => 'nullable|integer|exists:cities,id',
            'postal_code' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
        ]);

        // Only update password if provided
        if ($request->filled('password')) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $validated['is_active'] = (bool) $request->input('is_active', false);

        // Get location names from IDs
        $countryName = null;
        $stateName = null;
        $cityName = null;

        if (!empty($validated['location']['country'])) {
            $country = \App\Models\Country::find($validated['location']['country']);
            $countryName = $country ? $country->name : null;
        }

        if (!empty($validated['location']['state'])) {
            $state = \App\Models\State::find($validated['location']['state']);
            $stateName = $state ? $state->name : null;
        }

        if (!empty($validated['location']['city'])) {
            $city = \App\Models\City::find($validated['location']['city']);
            $cityName = $city ? $city->name : null;
        }

        // Remove location array and add individual fields
        unset($validated['location']);
        $validated['country'] = $countryName;
        $validated['state'] = $stateName;
        $validated['city'] = $cityName;

        $user->update($validated);

        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): RedirectResponse
    {
        // Prevent deletion of current user
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                           ->with('error', 'You cannot delete your own account.');
        }

        // Check if user has orders or parcels
        if ($user->orders()->count() > 0 || $user->parcels()->count() > 0) {
            return redirect()->route('admin.users.index')
                           ->with('error', 'Cannot delete user with existing orders or parcels. Deactivate instead.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
                        ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status
     */
    public function toggleStatus(User $user): JsonResponse
    {
        // Prevent deactivating current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot deactivate your own account.',
            ], 400);
        }

        $user->update(['is_active' => !$user->is_active]);

        return response()->json([
            'success' => true,
            'status' => $user->is_active,
            'message' => 'User status updated successfully.',
        ]);
    }

    /**
     * Update user role
     */
    public function updateRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'role' => ['required', Rule::in(['admin', 'staff', 'customer'])],
        ]);

        // Prevent changing own role
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot change your own role.',
            ], 400);
        }

        $oldRole = $user->role;
        $user->update(['role' => $request->role]);

        return response()->json([
            'success' => true,
            'old_role' => $oldRole,
            'new_role' => $user->role,
            'message' => 'User role updated successfully.',
        ]);
    }

    /**
     * Bulk actions for users
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'action' => 'required|in:activate,deactivate,delete,change_role',
            'role' => 'required_if:action,change_role|in:admin,staff,customer',
        ]);

        $users = User::whereIn('id', $request->user_ids)
                    ->where('id', '!=', auth()->id()) // Exclude current user
                    ->get();

        $count = 0;

        foreach ($users as $user) {
            switch ($request->action) {
                case 'activate':
                    $user->update(['is_active' => true]);
                    $count++;
                    break;
                case 'deactivate':
                    $user->update(['is_active' => false]);
                    $count++;
                    break;
                case 'delete':
                    if ($user->orders()->count() === 0 && $user->parcels()->count() === 0) {
                        $user->delete();
                        $count++;
                    }
                    break;
                case 'change_role':
                    $user->update(['role' => $request->role]);
                    $count++;
                    break;
            }
        }

        return response()->json([
            'success' => true,
            'count' => $count,
            'message' => "Successfully processed {$count} users.",
        ]);
    }

    /**
     * Reset user password
     */
    public function resetPassword(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password reset successfully.',
        ]);
    }
}
