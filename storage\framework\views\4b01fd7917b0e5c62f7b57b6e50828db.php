<?php $__env->startSection('title', 'Test Location Dropdown'); ?>

<?php $__env->startSection('content'); ?>
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Test Location Dropdown</h3>
                </div>
                <div class="card-body">
                    <form>
                        <h5>Bootstrap Style</h5>
                        <?php if (isset($component)) { $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.location-dropdown','data' => ['name' => 'test_location_bootstrap','style' => 'bootstrap']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('location-dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_location_bootstrap','style' => 'bootstrap']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $attributes = $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $component = $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <h5>Tailwind Style</h5>
                        <?php if (isset($component)) { $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.location-dropdown','data' => ['name' => 'test_location_tailwind','style' => 'tailwind']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('location-dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_location_tailwind','style' => 'tailwind']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $attributes = $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $component = $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <h5>With Initial Values</h5>
                        <?php if (isset($component)) { $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.location-dropdown','data' => ['name' => 'test_location_initial','style' => 'bootstrap','countryValue' => 1,'stateValue' => 1,'cityValue' => 1]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('location-dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_location_initial','style' => 'bootstrap','country-value' => 1,'state-value' => 1,'city-value' => 1]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $attributes = $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $component = $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary" onclick="getValues()">Get Values</button>
                            <button type="button" class="btn btn-secondary" onclick="resetDropdowns()">Reset</button>
                        </div>
                        
                        <div id="output" class="mt-3"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function getValues() {
    const containers = document.querySelectorAll('.location-dropdown-container');
    const output = document.getElementById('output');
    let html = '<h6>Current Values:</h6>';
    
    containers.forEach(container => {
        const name = container.getAttribute('data-name');
        const country = container.querySelector('.location-country-select')?.value || '';
        const state = container.querySelector('.location-state-select')?.value || '';
        const city = container.querySelector('.location-city-select')?.value || '';
        
        html += `<p><strong>${name}:</strong> Country: ${country}, State: ${state}, City: ${city}</p>`;
    });
    
    output.innerHTML = html;
}

function resetDropdowns() {
    const containers = document.querySelectorAll('.location-dropdown-container');
    containers.forEach(container => {
        const country = container.querySelector('.location-country-select');
        const state = container.querySelector('.location-state-select');
        const city = container.querySelector('.location-city-select');
        
        if (country) country.value = '';
        if (state) {
            state.innerHTML = '<option value="">Select State/Province</option>';
            state.disabled = true;
        }
        if (city) {
            city.innerHTML = '<option value="">Select City</option>';
            city.disabled = true;
        }
    });
    
    document.getElementById('output').innerHTML = '';
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/test-location.blade.php ENDPATH**/ ?>