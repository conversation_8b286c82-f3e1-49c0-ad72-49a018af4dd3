<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'location',
    'label' => 'Location',
    'required' => false,
    'placeholder' => 'Select...',
    'value' => null,
    'countryValue' => null,
    'stateValue' => null,
    'cityValue' => null,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'containerClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => 'text-red-500 text-sm mt-1',
    'loadingClass' => 'text-gray-500 text-sm',
    'searchable' => true,
    'clearable' => true,
    'disabled' => false,
    'readonly' => false,
    'id' => null,
    'style' => 'tailwind' // 'tailwind' or 'bootstrap'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'location',
    'label' => 'Location',
    'required' => false,
    'placeholder' => 'Select...',
    'value' => null,
    'countryValue' => null,
    'stateValue' => null,
    'cityValue' => null,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'containerClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => 'text-red-500 text-sm mt-1',
    'loadingClass' => 'text-gray-500 text-sm',
    'searchable' => true,
    'clearable' => true,
    'disabled' => false,
    'readonly' => false,
    'id' => null,
    'style' => 'tailwind' // 'tailwind' or 'bootstrap'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $baseId = $id ?? $name;
    $countryId = $baseId . '_country';
    $stateId = $baseId . '_state';
    $cityId = $baseId . '_city';
    
    // Determine CSS classes based on style
    $selectClass = $style === 'bootstrap' 
        ? 'form-select' 
        : 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent';
    
    $labelBaseClass = $style === 'bootstrap'
        ? 'form-label'
        : 'block text-sm font-medium text-gray-700 mb-2';
        
    $containerBaseClass = $style === 'bootstrap'
        ? 'mb-3'
        : 'mb-4';
?>

<div class="location-dropdown-container <?php echo e($containerClass); ?>" data-name="<?php echo e($name); ?>">
    <?php if($showCountry): ?>
    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($countryId); ?>" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Country
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <select 
            name="<?php echo e($name); ?>[country]" 
            id="<?php echo e($countryId); ?>"
            class="location-country-select <?php echo e($selectClass); ?> <?php echo e($inputClass); ?>"
            data-target-state="<?php echo e($stateId); ?>"
            data-target-city="<?php echo e($cityId); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
            <option value=""><?php echo e($placeholder); ?></option>
        </select>
        <div class="location-error country-error <?php echo e($errorClass); ?>" style="display: none;"></div>
        <div class="location-loading country-loading <?php echo e($loadingClass); ?>" style="display: none;">Loading countries...</div>
    </div>
    <?php endif; ?>

    <?php if($showState): ?>
    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($stateId); ?>" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            State/Province
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <select 
            name="<?php echo e($name); ?>[state]" 
            id="<?php echo e($stateId); ?>"
            class="location-state-select <?php echo e($selectClass); ?> <?php echo e($inputClass); ?>"
            data-target-city="<?php echo e($cityId); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

            disabled
        >
            <option value="">Select country first</option>
        </select>
        <div class="location-error state-error <?php echo e($errorClass); ?>" style="display: none;"></div>
        <div class="location-loading state-loading <?php echo e($loadingClass); ?>" style="display: none;">Loading states...</div>
    </div>
    <?php endif; ?>

    <?php if($showCity): ?>
    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($cityId); ?>" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            City
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <select 
            name="<?php echo e($name); ?>[city]" 
            id="<?php echo e($cityId); ?>"
            class="location-city-select <?php echo e($selectClass); ?> <?php echo e($inputClass); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

            disabled
        >
            <option value="">Select state first</option>
        </select>
        <div class="location-error city-error <?php echo e($errorClass); ?>" style="display: none;"></div>
        <div class="location-loading city-loading <?php echo e($loadingClass); ?>" style="display: none;">Loading cities...</div>
    </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('[data-name="<?php echo e($name); ?>"]');

    if (container && window.LocationDropdown) {
        // Initialize the location dropdown
        window.LocationDropdown.init(container, {
            name: '<?php echo e($name); ?>',
            searchable: <?php echo e($searchable ? 'true' : 'false'); ?>,
            clearable: <?php echo e($clearable ? 'true' : 'false'); ?>,
            initialValues: {
                country: '<?php echo e($countryValue); ?>',
                state: '<?php echo e($stateValue); ?>',
                city: '<?php echo e($cityValue); ?>'
            },
            showCountry: <?php echo e($showCountry ? 'true' : 'false'); ?>,
            showState: <?php echo e($showState ? 'true' : 'false'); ?>,
            showCity: <?php echo e($showCity ? 'true' : 'false'); ?>,
            apiEndpoints: {
                countries: '/api/locations/countries',
                states: '/api/locations/countries/:country/states',
                cities: '/api/locations/states/:state/cities',
                search: '/api/locations/search'
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/components/location-dropdown.blade.php ENDPATH**/ ?>