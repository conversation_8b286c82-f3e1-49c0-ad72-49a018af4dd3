@extends('layouts.customer')

@section('title', 'Edit Profile')
@section('page-title', 'Edit Profile')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.profile.show') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Profile
        </a>
        <a href="{{ route('customer.profile.change-password') }}" class="btn btn-outline-warning">
            <i class="fas fa-key me-1"></i> Change Password
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('customer.profile.update') }}">
                @csrf
                @method('PUT')

                <!-- Personal Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Personal Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">Company Name</label>
                                <input type="text" class="form-control @error('company_name') is-invalid @enderror" 
                                       id="company_name" name="company_name" value="{{ old('company_name', $user->company_name) }}">
                                @error('company_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Address Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="address" class="form-label">Street Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="2">{{ old('address', $user->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Location Dropdown Component -->
                        <x-location-dropdown
                            name="location"
                            style="bootstrap"
                            :country-value="old('location.country', $user->getCountryId())"
                            :state-value="old('location.state', $user->getStateId())"
                            :city-value="old('location.city', $user->getCityId())"
                        />

                        <div class="mb-3">
                            <label for="postal_code" class="form-label">Postal Code</label>
                            <input type="text" class="form-control @error('postal_code') is-invalid @enderror"
                                   id="postal_code" name="postal_code" value="{{ old('postal_code', $user->postal_code) }}">
                            @error('postal_code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ route('customer.profile.show') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update Profile
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Profile Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Profile Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Keep your contact information up to date for delivery notifications</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Add your company name for business shipping</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Complete address helps with accurate shipping quotes</small>
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Verify your email address to receive important updates</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Account Security -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Account Security
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Password</label>
                        <p class="mb-2">••••••••••••</p>
                        <small class="text-muted">Last changed: {{ $user->updated_at->diffForHumans() }}</small>
                    </div>
                    
                    <div class="d-grid">
                        <a href="{{ route('customer.profile.change-password') }}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>
                            Change Password
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        Quick Links
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('customer.profile.preferences') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-cog me-2"></i>
                            Notification Preferences
                        </a>
                        <a href="{{ route('customer.profile.address-book') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-address-book me-2"></i>
                            Address Book
                        </a>
                        <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Auto-format phone number
    document.getElementById('phone').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 6) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        } else if (value.length >= 3) {
            value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
        }
        e.target.value = value;
    });

    // Auto-format postal code
    document.getElementById('postal_code').addEventListener('input', function(e) {
        let value = e.target.value.toUpperCase();
        // US ZIP code format
        if (/^\d{5}$/.test(value)) {
            e.target.value = value;
        }
        // Canadian postal code format
        else if (/^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(value.replace(/\s/g, ''))) {
            value = value.replace(/\s/g, '');
            e.target.value = value.replace(/([A-Z]\d[A-Z])(\d[A-Z]\d)/, '$1 $2');
        }
    });

    // Country suggestions
    const countries = [
        'United States', 'Canada', 'United Kingdom', 'Australia', 'Germany', 
        'France', 'Italy', 'Spain', 'Netherlands', 'Belgium', 'Switzerland',
        'Austria', 'Sweden', 'Norway', 'Denmark', 'Finland', 'Japan', 'South Korea'
    ];

    const countryInput = document.getElementById('country');
    countryInput.addEventListener('input', function(e) {
        const value = e.target.value.toLowerCase();
        const suggestions = countries.filter(country => 
            country.toLowerCase().includes(value)
        );
        
        // You could implement a dropdown here for better UX
        if (suggestions.length === 1 && suggestions[0].toLowerCase() === value) {
            e.target.value = suggestions[0];
        }
    });
</script>
@endpush
