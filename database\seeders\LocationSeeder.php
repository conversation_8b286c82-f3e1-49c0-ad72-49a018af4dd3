<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedCountries();
    }

    private function seedCountries(): void
    {
        $countries = [
            // Major countries with comprehensive data
            [
                'name' => 'United States',
                'code' => 'USA',
                'iso2' => 'US',
                'phone_code' => '+1',
                'currency' => 'USD',
                'flag' => '🇺🇸',
                'sort_order' => 1,
                'states' => [
                    [
                        'name' => 'California',
                        'code' => 'CA',
                        'cities' => ['Los Angeles', 'San Francisco', 'San Diego', 'Sacramento', 'Oakland', 'Fresno', 'Long Beach', 'Santa Ana', 'Anaheim', 'Bakersfield']
                    ],
                    [
                        'name' => 'New York',
                        'code' => 'NY',
                        'cities' => ['New York City', 'Buffalo', 'Rochester', 'Yonkers', 'Syracuse', 'Albany', 'New Rochelle', 'Mount Vernon', 'Schenectady', 'Utica']
                    ],
                    [
                        'name' => 'Texas',
                        'code' => 'TX',
                        'cities' => ['Houston', 'San Antonio', 'Dallas', 'Austin', 'Fort Worth', 'El Paso', 'Arlington', 'Corpus Christi', 'Plano', 'Lubbock']
                    ],
                    [
                        'name' => 'Florida',
                        'code' => 'FL',
                        'cities' => ['Jacksonville', 'Miami', 'Tampa', 'Orlando', 'St. Petersburg', 'Hialeah', 'Tallahassee', 'Fort Lauderdale', 'Port St. Lucie', 'Cape Coral']
                    ],
                    [
                        'name' => 'Illinois',
                        'code' => 'IL',
                        'cities' => ['Chicago', 'Aurora', 'Rockford', 'Joliet', 'Naperville', 'Springfield', 'Peoria', 'Elgin', 'Waukegan', 'Cicero']
                    ]
                ]
            ],
            [
                'name' => 'Canada',
                'code' => 'CAN',
                'iso2' => 'CA',
                'phone_code' => '+1',
                'currency' => 'CAD',
                'flag' => '🇨🇦',
                'sort_order' => 2,
                'states' => [
                    [
                        'name' => 'Ontario',
                        'code' => 'ON',
                        'cities' => ['Toronto', 'Ottawa', 'Hamilton', 'London', 'Kitchener', 'Windsor', 'Oshawa', 'Barrie', 'Guelph', 'Kingston']
                    ],
                    [
                        'name' => 'Quebec',
                        'code' => 'QC',
                        'cities' => ['Montreal', 'Quebec City', 'Laval', 'Gatineau', 'Longueuil', 'Sherbrooke', 'Saguenay', 'Lévis', 'Trois-Rivières', 'Terrebonne']
                    ],
                    [
                        'name' => 'British Columbia',
                        'code' => 'BC',
                        'cities' => ['Vancouver', 'Victoria', 'Surrey', 'Burnaby', 'Richmond', 'Abbotsford', 'Coquitlam', 'Kelowna', 'Saanich', 'Delta']
                    ],
                    [
                        'name' => 'Alberta',
                        'code' => 'AB',
                        'cities' => ['Calgary', 'Edmonton', 'Red Deer', 'Lethbridge', 'St. Albert', 'Medicine Hat', 'Grande Prairie', 'Airdrie', 'Spruce Grove', 'Leduc']
                    ]
                ]
            ],
            [
                'name' => 'United Kingdom',
                'code' => 'GBR',
                'iso2' => 'GB',
                'phone_code' => '+44',
                'currency' => 'GBP',
                'flag' => '🇬🇧',
                'sort_order' => 3,
                'states' => [
                    [
                        'name' => 'England',
                        'code' => 'ENG',
                        'cities' => ['London', 'Birmingham', 'Manchester', 'Liverpool', 'Leeds', 'Sheffield', 'Bristol', 'Newcastle', 'Nottingham', 'Leicester']
                    ],
                    [
                        'name' => 'Scotland',
                        'code' => 'SCT',
                        'cities' => ['Edinburgh', 'Glasgow', 'Aberdeen', 'Dundee', 'Stirling', 'Perth', 'Inverness', 'Paisley', 'East Kilbride', 'Hamilton']
                    ],
                    [
                        'name' => 'Wales',
                        'code' => 'WLS',
                        'cities' => ['Cardiff', 'Swansea', 'Newport', 'Wrexham', 'Barry', 'Caerphilly', 'Bridgend', 'Neath', 'Port Talbot', 'Cwmbran']
                    ],
                    [
                        'name' => 'Northern Ireland',
                        'code' => 'NIR',
                        'cities' => ['Belfast', 'Derry', 'Lisburn', 'Newtownabbey', 'Bangor', 'Craigavon', 'Castlereagh', 'Ballymena', 'Newtownards', 'Carrickfergus']
                    ]
                ]
            ]
        ];

        foreach ($countries as $countryData) {
            $states = $countryData['states'] ?? [];
            unset($countryData['states']);

            $country = Country::create($countryData);

            foreach ($states as $stateData) {
                $cities = $stateData['cities'] ?? [];
                unset($stateData['cities']);
                $stateData['country_id'] = $country->id;

                $state = State::create($stateData);

                foreach ($cities as $cityName) {
                    City::create([
                        'state_id' => $state->id,
                        'name' => $cityName,
                    ]);
                }
            }
        }

        // Add more countries without detailed states/cities for now
        $additionalCountries = [
            ['name' => 'Australia', 'code' => 'AUS', 'iso2' => 'AU', 'phone_code' => '+61', 'currency' => 'AUD', 'flag' => '🇦🇺', 'sort_order' => 4],
            ['name' => 'Germany', 'code' => 'DEU', 'iso2' => 'DE', 'phone_code' => '+49', 'currency' => 'EUR', 'flag' => '🇩🇪', 'sort_order' => 5],
            ['name' => 'France', 'code' => 'FRA', 'iso2' => 'FR', 'phone_code' => '+33', 'currency' => 'EUR', 'flag' => '🇫🇷', 'sort_order' => 6],
            ['name' => 'Italy', 'code' => 'ITA', 'iso2' => 'IT', 'phone_code' => '+39', 'currency' => 'EUR', 'flag' => '🇮🇹', 'sort_order' => 7],
            ['name' => 'Spain', 'code' => 'ESP', 'iso2' => 'ES', 'phone_code' => '+34', 'currency' => 'EUR', 'flag' => '🇪🇸', 'sort_order' => 8],
            ['name' => 'Netherlands', 'code' => 'NLD', 'iso2' => 'NL', 'phone_code' => '+31', 'currency' => 'EUR', 'flag' => '🇳🇱', 'sort_order' => 9],
            ['name' => 'Belgium', 'code' => 'BEL', 'iso2' => 'BE', 'phone_code' => '+32', 'currency' => 'EUR', 'flag' => '🇧🇪', 'sort_order' => 10],
            ['name' => 'Switzerland', 'code' => 'CHE', 'iso2' => 'CH', 'phone_code' => '+41', 'currency' => 'CHF', 'flag' => '🇨🇭', 'sort_order' => 11],
            ['name' => 'Austria', 'code' => 'AUT', 'iso2' => 'AT', 'phone_code' => '+43', 'currency' => 'EUR', 'flag' => '🇦🇹', 'sort_order' => 12],
            ['name' => 'Sweden', 'code' => 'SWE', 'iso2' => 'SE', 'phone_code' => '+46', 'currency' => 'SEK', 'flag' => '🇸🇪', 'sort_order' => 13],
            ['name' => 'Norway', 'code' => 'NOR', 'iso2' => 'NO', 'phone_code' => '+47', 'currency' => 'NOK', 'flag' => '🇳🇴', 'sort_order' => 14],
            ['name' => 'Denmark', 'code' => 'DNK', 'iso2' => 'DK', 'phone_code' => '+45', 'currency' => 'DKK', 'flag' => '🇩🇰', 'sort_order' => 15],
            ['name' => 'Finland', 'code' => 'FIN', 'iso2' => 'FI', 'phone_code' => '+358', 'currency' => 'EUR', 'flag' => '🇫🇮', 'sort_order' => 16],
            ['name' => 'Japan', 'code' => 'JPN', 'iso2' => 'JP', 'phone_code' => '+81', 'currency' => 'JPY', 'flag' => '🇯🇵', 'sort_order' => 17],
            ['name' => 'South Korea', 'code' => 'KOR', 'iso2' => 'KR', 'phone_code' => '+82', 'currency' => 'KRW', 'flag' => '🇰🇷', 'sort_order' => 18],
            ['name' => 'China', 'code' => 'CHN', 'iso2' => 'CN', 'phone_code' => '+86', 'currency' => 'CNY', 'flag' => '🇨🇳', 'sort_order' => 19],
            ['name' => 'India', 'code' => 'IND', 'iso2' => 'IN', 'phone_code' => '+91', 'currency' => 'INR', 'flag' => '🇮🇳', 'sort_order' => 20],
            ['name' => 'Brazil', 'code' => 'BRA', 'iso2' => 'BR', 'phone_code' => '+55', 'currency' => 'BRL', 'flag' => '🇧🇷', 'sort_order' => 21],
            ['name' => 'Mexico', 'code' => 'MEX', 'iso2' => 'MX', 'phone_code' => '+52', 'currency' => 'MXN', 'flag' => '🇲🇽', 'sort_order' => 22],
        ];

        foreach ($additionalCountries as $countryData) {
            Country::create($countryData);
        }
    }
}
