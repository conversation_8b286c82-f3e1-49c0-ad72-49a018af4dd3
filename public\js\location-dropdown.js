/**
 * Location Dropdown Library
 * Handles cascading Country -> State -> City dropdowns
 */
window.LocationDropdown = (function() {
    'use strict';

    // Default configuration
    const defaults = {
        searchable: true,
        clearable: true,
        showCountry: true,
        showState: true,
        showCity: true,
        initialValues: {
            country: '',
            state: '',
            city: ''
        },
        apiEndpoints: {
            countries: '/api/locations/countries',
            states: '/api/locations/countries/:country/states',
            cities: '/api/locations/states/:state/cities',
            search: '/api/locations/search'
        },
        debounceDelay: 300,
        loadingText: {
            countries: 'Loading countries...',
            states: 'Loading states...',
            cities: 'Loading cities...'
        },
        placeholders: {
            country: 'Select Country',
            state: 'Select State/Province',
            city: 'Select City'
        }
    };

    // Cache for API responses
    const cache = new Map();

    /**
     * Initialize location dropdown
     */
    function init(container, options = {}) {
        const config = Object.assign({}, defaults, options);
        const instance = new LocationDropdownInstance(container, config);
        return instance;
    }

    /**
     * Location Dropdown Instance
     */
    function LocationDropdownInstance(container, config) {
        this.container = container;
        this.config = config;
        this.elements = {};
        this.isLoading = false;
        
        this.init();
    }

    LocationDropdownInstance.prototype = {
        init: function() {
            this.findElements();
            this.bindEvents();
            this.loadCountries();
            this.setInitialValues();
        },

        findElements: function() {
            this.elements.country = this.container.querySelector('.location-country-select');
            this.elements.state = this.container.querySelector('.location-state-select');
            this.elements.city = this.container.querySelector('.location-city-select');
            
            this.elements.countryError = this.container.querySelector('.country-error');
            this.elements.stateError = this.container.querySelector('.state-error');
            this.elements.cityError = this.container.querySelector('.city-error');
            
            this.elements.countryLoading = this.container.querySelector('.country-loading');
            this.elements.stateLoading = this.container.querySelector('.state-loading');
            this.elements.cityLoading = this.container.querySelector('.city-loading');
        },

        bindEvents: function() {
            if (this.elements.country) {
                this.elements.country.addEventListener('change', (e) => {
                    this.onCountryChange(e.target.value);
                });
            }

            if (this.elements.state) {
                this.elements.state.addEventListener('change', (e) => {
                    this.onStateChange(e.target.value);
                });
            }

            if (this.elements.city) {
                this.elements.city.addEventListener('change', (e) => {
                    this.onCityChange(e.target.value);
                });
            }
        },

        loadCountries: function() {
            if (!this.elements.country) return;

            this.showLoading('country');
            this.hideError('country');

            const cacheKey = 'countries';
            if (cache.has(cacheKey)) {
                this.populateCountries(cache.get(cacheKey));
                this.hideLoading('country');
                return;
            }

            fetch(this.config.apiEndpoints.countries)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cache.set(cacheKey, data.data);
                        this.populateCountries(data.data);
                    } else {
                        this.showError('country', 'Failed to load countries');
                    }
                })
                .catch(error => {
                    console.error('Error loading countries:', error);
                    this.showError('country', 'Failed to load countries');
                })
                .finally(() => {
                    this.hideLoading('country');
                });
        },

        loadStates: function(countryId) {
            if (!this.elements.state || !countryId) return;

            this.showLoading('state');
            this.hideError('state');
            this.clearSelect('state');
            this.clearSelect('city');

            const cacheKey = `states_${countryId}`;
            if (cache.has(cacheKey)) {
                this.populateStates(cache.get(cacheKey));
                this.hideLoading('state');
                return;
            }

            const url = this.config.apiEndpoints.states.replace(':country', countryId);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cache.set(cacheKey, data.data);
                        this.populateStates(data.data);
                    } else {
                        this.showError('state', 'Failed to load states');
                    }
                })
                .catch(error => {
                    console.error('Error loading states:', error);
                    this.showError('state', 'Failed to load states');
                })
                .finally(() => {
                    this.hideLoading('state');
                });
        },

        loadCities: function(stateId) {
            if (!this.elements.city || !stateId) return;

            this.showLoading('city');
            this.hideError('city');
            this.clearSelect('city');

            const cacheKey = `cities_${stateId}`;
            if (cache.has(cacheKey)) {
                this.populateCities(cache.get(cacheKey));
                this.hideLoading('city');
                return;
            }

            const url = this.config.apiEndpoints.cities.replace(':state', stateId);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cache.set(cacheKey, data.data);
                        this.populateCities(data.data);
                    } else {
                        this.showError('city', 'Failed to load cities');
                    }
                })
                .catch(error => {
                    console.error('Error loading cities:', error);
                    this.showError('city', 'Failed to load cities');
                })
                .finally(() => {
                    this.hideLoading('city');
                });
        },

        populateCountries: function(countries) {
            if (!this.elements.country) return;

            this.clearSelect('country');
            this.addOption(this.elements.country, '', this.config.placeholders.country);

            countries.forEach(country => {
                this.addOption(this.elements.country, country.id, country.name, {
                    'data-code': country.code,
                    'data-iso2': country.iso2,
                    'data-phone': country.phone_code,
                    'data-currency': country.currency,
                    'data-flag': country.flag
                });
            });
        },

        populateStates: function(states) {
            if (!this.elements.state) return;

            this.clearSelect('state');
            this.addOption(this.elements.state, '', this.config.placeholders.state);

            states.forEach(state => {
                this.addOption(this.elements.state, state.id, state.name, {
                    'data-code': state.code
                });
            });

            this.elements.state.disabled = false;
        },

        populateCities: function(cities) {
            if (!this.elements.city) return;

            this.clearSelect('city');
            this.addOption(this.elements.city, '', this.config.placeholders.city);

            cities.forEach(city => {
                this.addOption(this.elements.city, city.id, city.name);
            });

            this.elements.city.disabled = false;
        },

        addOption: function(select, value, text, attributes = {}) {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            
            Object.keys(attributes).forEach(attr => {
                option.setAttribute(attr, attributes[attr]);
            });
            
            select.appendChild(option);
        },

        clearSelect: function(type) {
            const element = this.elements[type];
            if (!element) return;

            element.innerHTML = '';
            element.disabled = true;
            
            const placeholder = this.config.placeholders[type];
            this.addOption(element, '', placeholder);
        },

        onCountryChange: function(countryId) {
            this.hideError('country');
            
            if (countryId) {
                this.loadStates(countryId);
            } else {
                this.clearSelect('state');
                this.clearSelect('city');
            }

            this.triggerChangeEvent('country', countryId);
        },

        onStateChange: function(stateId) {
            this.hideError('state');
            
            if (stateId) {
                this.loadCities(stateId);
            } else {
                this.clearSelect('city');
            }

            this.triggerChangeEvent('state', stateId);
        },

        onCityChange: function(cityId) {
            this.hideError('city');
            this.triggerChangeEvent('city', cityId);
        },

        setInitialValues: function() {
            const { country, state, city } = this.config.initialValues;
            
            if (country) {
                setTimeout(() => {
                    if (this.elements.country) {
                        this.elements.country.value = country;
                        this.onCountryChange(country);
                        
                        if (state) {
                            setTimeout(() => {
                                if (this.elements.state) {
                                    this.elements.state.value = state;
                                    this.onStateChange(state);
                                    
                                    if (city) {
                                        setTimeout(() => {
                                            if (this.elements.city) {
                                                this.elements.city.value = city;
                                            }
                                        }, 500);
                                    }
                                }
                            }, 500);
                        }
                    }
                }, 500);
            }
        },

        showLoading: function(type) {
            const element = this.elements[type + 'Loading'];
            if (element) {
                element.style.display = 'block';
            }
        },

        hideLoading: function(type) {
            const element = this.elements[type + 'Loading'];
            if (element) {
                element.style.display = 'none';
            }
        },

        showError: function(type, message) {
            const element = this.elements[type + 'Error'];
            if (element) {
                element.textContent = message;
                element.style.display = 'block';
            }
        },

        hideError: function(type) {
            const element = this.elements[type + 'Error'];
            if (element) {
                element.style.display = 'none';
            }
        },

        triggerChangeEvent: function(type, value) {
            const event = new CustomEvent('locationChange', {
                detail: {
                    type: type,
                    value: value,
                    instance: this
                }
            });
            this.container.dispatchEvent(event);
        },

        // Public methods
        getValues: function() {
            return {
                country: this.elements.country ? this.elements.country.value : '',
                state: this.elements.state ? this.elements.state.value : '',
                city: this.elements.city ? this.elements.city.value : ''
            };
        },

        setValues: function(values) {
            this.config.initialValues = Object.assign({}, this.config.initialValues, values);
            this.setInitialValues();
        },

        reset: function() {
            this.clearSelect('country');
            this.clearSelect('state');
            this.clearSelect('city');
            this.loadCountries();
        },

        destroy: function() {
            // Remove event listeners and clean up
            this.container.removeEventListener('locationChange', this.onLocationChange);
        }
    };

    // Public API
    return {
        init: init,
        cache: cache
    };
})();
