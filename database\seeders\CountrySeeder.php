<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Country;
use App\Models\State;
use App\Models\City;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create United States
        $usa = Country::create([
            'name' => 'United States',
            'code' => 'USA',
            'iso2' => 'US',
            'phone_code' => '+1',
            'currency' => 'USD',
            'flag' => '🇺🇸',
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create some US states
        $california = State::create([
            'country_id' => $usa->id,
            'name' => 'California',
            'code' => 'CA',
            'is_active' => true,
            'sort_order' => 1
        ]);

        $texas = State::create([
            'country_id' => $usa->id,
            'name' => 'Texas',
            'code' => 'TX',
            'is_active' => true,
            'sort_order' => 2
        ]);

        $newyork = State::create([
            'country_id' => $usa->id,
            'name' => 'New York',
            'code' => 'NY',
            'is_active' => true,
            'sort_order' => 3
        ]);

        // Create some cities for California
        City::create([
            'state_id' => $california->id,
            'name' => 'Los Angeles',
            'is_active' => true,
            'sort_order' => 1
        ]);

        City::create([
            'state_id' => $california->id,
            'name' => 'San Francisco',
            'is_active' => true,
            'sort_order' => 2
        ]);

        City::create([
            'state_id' => $california->id,
            'name' => 'San Diego',
            'is_active' => true,
            'sort_order' => 3
        ]);

        // Create some cities for Texas
        City::create([
            'state_id' => $texas->id,
            'name' => 'Houston',
            'is_active' => true,
            'sort_order' => 1
        ]);

        City::create([
            'state_id' => $texas->id,
            'name' => 'Dallas',
            'is_active' => true,
            'sort_order' => 2
        ]);

        City::create([
            'state_id' => $texas->id,
            'name' => 'Austin',
            'is_active' => true,
            'sort_order' => 3
        ]);

        // Create some cities for New York
        City::create([
            'state_id' => $newyork->id,
            'name' => 'New York City',
            'is_active' => true,
            'sort_order' => 1
        ]);

        City::create([
            'state_id' => $newyork->id,
            'name' => 'Buffalo',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create Canada
        $canada = Country::create([
            'name' => 'Canada',
            'code' => 'CAN',
            'iso2' => 'CA',
            'phone_code' => '+1',
            'currency' => 'CAD',
            'flag' => '🇨🇦',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create some Canadian provinces
        $ontario = State::create([
            'country_id' => $canada->id,
            'name' => 'Ontario',
            'code' => 'ON',
            'is_active' => true,
            'sort_order' => 1
        ]);

        $quebec = State::create([
            'country_id' => $canada->id,
            'name' => 'Quebec',
            'code' => 'QC',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create some cities for Ontario
        City::create([
            'state_id' => $ontario->id,
            'name' => 'Toronto',
            'is_active' => true,
            'sort_order' => 1
        ]);

        City::create([
            'state_id' => $ontario->id,
            'name' => 'Ottawa',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create some cities for Quebec
        City::create([
            'state_id' => $quebec->id,
            'name' => 'Montreal',
            'is_active' => true,
            'sort_order' => 1
        ]);

        City::create([
            'state_id' => $quebec->id,
            'name' => 'Quebec City',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create United Kingdom
        $uk = Country::create([
            'name' => 'United Kingdom',
            'code' => 'GBR',
            'iso2' => 'GB',
            'phone_code' => '+44',
            'currency' => 'GBP',
            'flag' => '🇬🇧',
            'is_active' => true,
            'sort_order' => 3
        ]);

        // Create some UK regions
        $england = State::create([
            'country_id' => $uk->id,
            'name' => 'England',
            'code' => 'ENG',
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create some cities for England
        City::create([
            'state_id' => $england->id,
            'name' => 'London',
            'is_active' => true,
            'sort_order' => 1
        ]);

        City::create([
            'state_id' => $england->id,
            'name' => 'Manchester',
            'is_active' => true,
            'sort_order' => 2
        ]);

        City::create([
            'state_id' => $england->id,
            'name' => 'Birmingham',
            'is_active' => true,
            'sort_order' => 3
        ]);
    }
}
