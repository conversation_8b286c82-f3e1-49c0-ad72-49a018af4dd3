<?php $__env->startSection('title', 'Test Tom Select Location Dropdown'); ?>

<?php $__env->startSection('content'); ?>
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h3>Test Tom Select Location Dropdown</h3>
                </div>
                <div class="card-body">
                    <form>
                        <h5>Bootstrap Style</h5>
                        <?php if (isset($component)) { $__componentOriginalae7beaa680e2620948890e093414b8d0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalae7beaa680e2620948890e093414b8d0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tom-select-location','data' => ['name' => 'test_location_bootstrap','style' => 'bootstrap']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('tom-select-location'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_location_bootstrap','style' => 'bootstrap']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalae7beaa680e2620948890e093414b8d0)): ?>
<?php $attributes = $__attributesOriginalae7beaa680e2620948890e093414b8d0; ?>
<?php unset($__attributesOriginalae7beaa680e2620948890e093414b8d0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalae7beaa680e2620948890e093414b8d0)): ?>
<?php $component = $__componentOriginalae7beaa680e2620948890e093414b8d0; ?>
<?php unset($__componentOriginalae7beaa680e2620948890e093414b8d0); ?>
<?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <h5>Tailwind Style</h5>
                        <?php if (isset($component)) { $__componentOriginalae7beaa680e2620948890e093414b8d0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalae7beaa680e2620948890e093414b8d0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tom-select-location','data' => ['name' => 'test_location_tailwind','style' => 'tailwind']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('tom-select-location'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_location_tailwind','style' => 'tailwind']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalae7beaa680e2620948890e093414b8d0)): ?>
<?php $attributes = $__attributesOriginalae7beaa680e2620948890e093414b8d0; ?>
<?php unset($__attributesOriginalae7beaa680e2620948890e093414b8d0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalae7beaa680e2620948890e093414b8d0)): ?>
<?php $component = $__componentOriginalae7beaa680e2620948890e093414b8d0; ?>
<?php unset($__componentOriginalae7beaa680e2620948890e093414b8d0); ?>
<?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <h5>With Initial Values</h5>
                        <?php if (isset($component)) { $__componentOriginalae7beaa680e2620948890e093414b8d0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalae7beaa680e2620948890e093414b8d0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tom-select-location','data' => ['name' => 'test_location_initial','style' => 'bootstrap','countryValue' => 'United States','stateValue' => 'California','cityValue' => 'Los Angeles']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('tom-select-location'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'test_location_initial','style' => 'bootstrap','country-value' => 'United States','state-value' => 'California','city-value' => 'Los Angeles']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalae7beaa680e2620948890e093414b8d0)): ?>
<?php $attributes = $__attributesOriginalae7beaa680e2620948890e093414b8d0; ?>
<?php unset($__attributesOriginalae7beaa680e2620948890e093414b8d0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalae7beaa680e2620948890e093414b8d0)): ?>
<?php $component = $__componentOriginalae7beaa680e2620948890e093414b8d0; ?>
<?php unset($__componentOriginalae7beaa680e2620948890e093414b8d0); ?>
<?php endif; ?>
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary" onclick="getValues()">Get Values</button>
                            <button type="button" class="btn btn-secondary" onclick="resetDropdowns()">Reset</button>
                        </div>
                        
                        <div id="output" class="mt-3"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function getValues() {
    const containers = document.querySelectorAll('[data-location-container]');
    const output = document.getElementById('output');
    let html = '<h6>Current Values:</h6>';
    
    containers.forEach(container => {
        const name = container.getAttribute('data-location-container');
        const countrySelect = container.querySelector('[data-location-type="country"]');
        const stateSelect = container.querySelector('[data-location-type="state"]');
        const citySelect = container.querySelector('[data-location-type="city"]');
        
        const country = countrySelect ? countrySelect.value : '';
        const state = stateSelect ? stateSelect.value : '';
        const city = citySelect ? citySelect.value : '';
        
        html += `<p><strong>${name}:</strong> Country: ${country}, State: ${state}, City: ${city}</p>`;
    });
    
    output.innerHTML = html;
}

function resetDropdowns() {
    const containers = document.querySelectorAll('[data-location-container]');
    containers.forEach(container => {
        const countrySelect = container.querySelector('[data-location-type="country"]');
        const stateSelect = container.querySelector('[data-location-type="state"]');
        const citySelect = container.querySelector('[data-location-type="city"]');
        
        // Reset Tom Select instances
        if (countrySelect && countrySelect.tomselect) {
            countrySelect.tomselect.clear();
        }
        if (stateSelect && stateSelect.tomselect) {
            stateSelect.tomselect.clear();
            stateSelect.tomselect.clearOptions();
            stateSelect.tomselect.disable();
        }
        if (citySelect && citySelect.tomselect) {
            citySelect.tomselect.clear();
            citySelect.tomselect.clearOptions();
            citySelect.tomselect.disable();
        }
    });
    
    document.getElementById('output').innerHTML = '';
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/test-tom-select.blade.php ENDPATH**/ ?>