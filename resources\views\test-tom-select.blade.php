@extends('layouts.frontend')

@section('title', 'Test Tom Select Location Dropdown')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h3>Test Tom Select Location Dropdown</h3>
                </div>
                <div class="card-body">
                    <form>
                        <h5>Bootstrap Style</h5>
                        <x-tom-select-location 
                            name="test_location_bootstrap"
                            style="bootstrap"
                        />
                        
                        <hr class="my-4">
                        
                        <h5>Tailwind Style</h5>
                        <x-tom-select-location 
                            name="test_location_tailwind"
                            style="tailwind"
                        />
                        
                        <hr class="my-4">
                        
                        <h5>With Initial Values</h5>
                        <x-tom-select-location 
                            name="test_location_initial"
                            style="bootstrap"
                            country-value="United States"
                            state-value="California"
                            city-value="Los Angeles"
                        />
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary" onclick="getValues()">Get Values</button>
                            <button type="button" class="btn btn-secondary" onclick="resetDropdowns()">Reset</button>
                        </div>
                        
                        <div id="output" class="mt-3"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function getValues() {
    const containers = document.querySelectorAll('[data-location-container]');
    const output = document.getElementById('output');
    let html = '<h6>Current Values:</h6>';
    
    containers.forEach(container => {
        const name = container.getAttribute('data-location-container');
        const countrySelect = container.querySelector('[data-location-type="country"]');
        const stateSelect = container.querySelector('[data-location-type="state"]');
        const citySelect = container.querySelector('[data-location-type="city"]');
        
        const country = countrySelect ? countrySelect.value : '';
        const state = stateSelect ? stateSelect.value : '';
        const city = citySelect ? citySelect.value : '';
        
        html += `<p><strong>${name}:</strong> Country: ${country}, State: ${state}, City: ${city}</p>`;
    });
    
    output.innerHTML = html;
}

function resetDropdowns() {
    const containers = document.querySelectorAll('[data-location-container]');
    containers.forEach(container => {
        const countrySelect = container.querySelector('[data-location-type="country"]');
        const stateSelect = container.querySelector('[data-location-type="state"]');
        const citySelect = container.querySelector('[data-location-type="city"]');
        
        // Reset Tom Select instances
        if (countrySelect && countrySelect.tomselect) {
            countrySelect.tomselect.clear();
        }
        if (stateSelect && stateSelect.tomselect) {
            stateSelect.tomselect.clear();
            stateSelect.tomselect.clearOptions();
            stateSelect.tomselect.disable();
        }
        if (citySelect && citySelect.tomselect) {
            citySelect.tomselect.clear();
            citySelect.tomselect.clearOptions();
            citySelect.tomselect.disable();
        }
    });
    
    document.getElementById('output').innerHTML = '';
}
</script>
@endsection
