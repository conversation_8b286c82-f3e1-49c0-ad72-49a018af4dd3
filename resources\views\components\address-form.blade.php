@props([
    'name' => 'address',
    'label' => 'Address',
    'required' => false,
    'value' => null,
    'showPersonalInfo' => true,
    'showCompany' => true,
    'showPhone' => true,
    'showAddressType' => false,
    'addressTypes' => ['shipping' => 'Shipping', 'billing' => 'Billing', 'both' => 'Both'],
    'style' => 'tailwind', // 'tailwind' or 'bootstrap'
    'containerClass' => '',
    'gridClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => '',
    'disabled' => false,
    'readonly' => false
])

@php
    $baseId = $name;
    
    // Determine CSS classes based on style
    $inputBaseClass = $style === 'bootstrap' 
        ? 'form-control' 
        : 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent';
    
    $selectBaseClass = $style === 'bootstrap' 
        ? 'form-select' 
        : 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent';
    
    $labelBaseClass = $style === 'bootstrap'
        ? 'form-label'
        : 'block text-sm font-medium text-gray-700 mb-2';
        
    $containerBaseClass = $style === 'bootstrap'
        ? 'mb-3'
        : 'mb-4';
        
    $gridBaseClass = $style === 'bootstrap'
        ? 'row'
        : 'grid grid-cols-1 md:grid-cols-2 gap-4';
        
    $colClass = $style === 'bootstrap'
        ? 'col-md-6'
        : '';

    // Get values from old input or passed value
    $addressData = old($name, $value ?? []);
@endphp

<div class="address-form-container {{ $containerClass }}" data-name="{{ $name }}">
    @if($showAddressType)
    <div class="{{ $containerBaseClass }}">
        <label for="{{ $baseId }}_type" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Address Type
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <select 
            name="{{ $name }}[type]" 
            id="{{ $baseId }}_type"
            class="{{ $selectBaseClass }} {{ $inputClass }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
            <option value="">Select Type</option>
            @foreach($addressTypes as $typeValue => $typeLabel)
                <option value="{{ $typeValue }}" {{ (($addressData['type'] ?? '') === $typeValue) ? 'selected' : '' }}>
                    {{ $typeLabel }}
                </option>
            @endforeach
        </select>
        @error($name . '.type')
            <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
        @enderror
    </div>
    @endif

    @if($showPersonalInfo)
    <div class="{{ $gridBaseClass }} {{ $gridClass }}">
        <div class="{{ $colClass }} {{ $containerBaseClass }}">
            <label for="{{ $baseId }}_first_name" class="{{ $labelBaseClass }} {{ $labelClass }}">
                First Name
                @if($required)<span class="text-red-500">*</span>@endif
            </label>
            <input 
                type="text" 
                name="{{ $name }}[first_name]" 
                id="{{ $baseId }}_first_name"
                class="{{ $inputBaseClass }} {{ $inputClass }}"
                value="{{ $addressData['first_name'] ?? '' }}"
                {{ $required ? 'required' : '' }}
                {{ $disabled ? 'disabled' : '' }}
                {{ $readonly ? 'readonly' : '' }}
            >
            @error($name . '.first_name')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
            @enderror
        </div>
        
        <div class="{{ $colClass }} {{ $containerBaseClass }}">
            <label for="{{ $baseId }}_last_name" class="{{ $labelBaseClass }} {{ $labelClass }}">
                Last Name
                @if($required)<span class="text-red-500">*</span>@endif
            </label>
            <input 
                type="text" 
                name="{{ $name }}[last_name]" 
                id="{{ $baseId }}_last_name"
                class="{{ $inputBaseClass }} {{ $inputClass }}"
                value="{{ $addressData['last_name'] ?? '' }}"
                {{ $required ? 'required' : '' }}
                {{ $disabled ? 'disabled' : '' }}
                {{ $readonly ? 'readonly' : '' }}
            >
            @error($name . '.last_name')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
            @enderror
        </div>
    </div>
    @endif

    @if($showCompany)
    <div class="{{ $containerBaseClass }}">
        <label for="{{ $baseId }}_company" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Company (Optional)
        </label>
        <input 
            type="text" 
            name="{{ $name }}[company]" 
            id="{{ $baseId }}_company"
            class="{{ $inputBaseClass }} {{ $inputClass }}"
            value="{{ $addressData['company'] ?? '' }}"
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
        @error($name . '.company')
            <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
        @enderror
    </div>
    @endif

    <div class="{{ $containerBaseClass }}">
        <label for="{{ $baseId }}_address_line_1" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Address Line 1
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <input 
            type="text" 
            name="{{ $name }}[address_line_1]" 
            id="{{ $baseId }}_address_line_1"
            class="{{ $inputBaseClass }} {{ $inputClass }}"
            value="{{ $addressData['address_line_1'] ?? '' }}"
            placeholder="Street address, P.O. box, company name, c/o"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
        @error($name . '.address_line_1')
            <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
        @enderror
    </div>

    <div class="{{ $containerBaseClass }}">
        <label for="{{ $baseId }}_address_line_2" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Address Line 2 (Optional)
        </label>
        <input 
            type="text" 
            name="{{ $name }}[address_line_2]" 
            id="{{ $baseId }}_address_line_2"
            class="{{ $inputBaseClass }} {{ $inputClass }}"
            value="{{ $addressData['address_line_2'] ?? '' }}"
            placeholder="Apartment, suite, unit, building, floor, etc."
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
        @error($name . '.address_line_2')
            <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
        @enderror
    </div>

    <!-- Location Dropdown Component -->
    <x-location-dropdown 
        :name="$name"
        :required="$required"
        :style="$style"
        :country-value="$addressData['country'] ?? ''"
        :state-value="$addressData['state'] ?? ''"
        :city-value="$addressData['city'] ?? ''"
        :disabled="$disabled"
        :readonly="$readonly"
        :container-class="$containerClass"
        :input-class="$inputClass"
        :label-class="$labelClass"
        :error-class="$errorClass"
    />

    <div class="{{ $containerBaseClass }}">
        <label for="{{ $baseId }}_postal_code" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Postal Code
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <input 
            type="text" 
            name="{{ $name }}[postal_code]" 
            id="{{ $baseId }}_postal_code"
            class="{{ $inputBaseClass }} {{ $inputClass }}"
            value="{{ $addressData['postal_code'] ?? '' }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
        @error($name . '.postal_code')
            <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
        @enderror
    </div>

    @if($showPhone)
    <div class="{{ $containerBaseClass }}">
        <label for="{{ $baseId }}_phone" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Phone Number
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <input 
            type="tel" 
            name="{{ $name }}[phone]" 
            id="{{ $baseId }}_phone"
            class="{{ $inputBaseClass }} {{ $inputClass }}"
            value="{{ $addressData['phone'] ?? '' }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
        @error($name . '.phone')
            <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
        @enderror
    </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addressForm = document.querySelector('[data-name="{{ $name }}"]');
    if (addressForm) {
        // Auto-format postal code based on country
        const postalCodeInput = addressForm.querySelector('#{{ $baseId }}_postal_code');
        const countrySelect = addressForm.querySelector('#{{ $name }}_country');
        
        if (postalCodeInput && countrySelect) {
            function formatPostalCode() {
                const countryOption = countrySelect.options[countrySelect.selectedIndex];
                const countryCode = countryOption ? countryOption.getAttribute('data-iso2') : '';
                let value = postalCodeInput.value.toUpperCase();
                
                // US ZIP code format
                if (countryCode === 'US' && /^\d{5}$/.test(value)) {
                    postalCodeInput.value = value;
                }
                // Canadian postal code format
                else if (countryCode === 'CA' && /^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(value.replace(/\s/g, ''))) {
                    value = value.replace(/\s/g, '');
                    postalCodeInput.value = value.replace(/([A-Z]\d[A-Z])(\d[A-Z]\d)/, '$1 $2');
                }
                // UK postal code format
                else if (countryCode === 'GB') {
                    // Basic UK postcode formatting
                    postalCodeInput.value = value;
                }
            }
            
            postalCodeInput.addEventListener('input', formatPostalCode);
            countrySelect.addEventListener('change', formatPostalCode);
        }
    }
});
</script>
@endpush
