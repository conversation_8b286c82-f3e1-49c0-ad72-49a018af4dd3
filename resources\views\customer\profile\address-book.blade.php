@extends('layouts.customer')

@section('title', 'Address Book')
@section('page-title', 'Address Book')

@section('page-actions')
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
    <i class="fas fa-plus me-2"></i>
    Add New Address
</button>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-address-book me-2"></i>
                    My Addresses
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Primary Address -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="fas fa-home me-2"></i>
                                    Primary Address
                                </span>
                                <span class="badge bg-light text-primary">Default</span>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">{{ $user->name }}</h6>
                                <p class="card-text">
                                    @if($user->address)
                                        {{ $user->address }}<br>
                                    @endif
                                    @if($user->city || $user->state || $user->postal_code)
                                        {{ $user->city }}@if($user->city && $user->state), @endif{{ $user->state }} {{ $user->postal_code }}<br>
                                    @endif
                                    @if($user->country)
                                        {{ $user->country }}
                                    @endif
                                </p>
                                @if($user->phone)
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-phone me-2"></i>
                                        {{ $user->phone }}
                                    </p>
                                @endif
                                <p class="text-muted mb-3">
                                    <i class="fas fa-envelope me-2"></i>
                                    {{ $user->email }}
                                </p>
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editAddress('primary')">
                                        <i class="fas fa-edit me-1"></i>
                                        Edit
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional addresses would go here -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card border-dashed h-100 d-flex align-items-center justify-content-center" style="border: 2px dashed #dee2e6; min-height: 250px;">
                            <div class="text-center text-muted">
                                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                                <h6>Add New Address</h6>
                                <p class="small">Click to add a new shipping address</p>
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                    Add Address
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                @if(!$user->address && !$user->city && !$user->state)
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Complete Your Profile:</strong> 
                        Add your address information to make shipping faster and easier.
                        <a href="{{ route('customer.profile.edit') }}" class="alert-link">Update Profile</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Add Address Modal -->
<div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAddressModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>
                    Add New Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addAddressForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="address_name" class="form-label">Address Name</label>
                            <input type="text" class="form-control" id="address_name" name="address_name" placeholder="e.g., Home, Office, etc." required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="recipient_name" class="form-label">Recipient Name</label>
                            <input type="text" class="form-control" id="recipient_name" name="recipient_name" value="{{ $user->name }}" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address_line_1" class="form-label">Address Line 1</label>
                        <input type="text" class="form-control" id="address_line_1" name="address_line_1" placeholder="Street address" required>
                    </div>
                    <div class="mb-3">
                        <label for="address_line_2" class="form-label">Address Line 2 (Optional)</label>
                        <input type="text" class="form-control" id="address_line_2" name="address_line_2" placeholder="Apartment, suite, etc.">
                    </div>
                    <!-- Location Dropdown Component -->
                    <x-location-dropdown
                        name="location"
                        style="bootstrap"
                        :required="true"
                    />

                    <div class="mb-3">
                        <label for="postal_code" class="form-label">Postal Code</label>
                        <input type="text" class="form-control" id="postal_code" name="postal_code" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="{{ $user->phone }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="address_type" class="form-label">Address Type</label>
                            <select class="form-select" id="address_type" name="address_type">
                                <option value="residential">Residential</option>
                                <option value="commercial">Commercial</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_default" name="is_default">
                        <label class="form-check-label" for="is_default">
                            Set as default address
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Save Address
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Address Modal -->
<div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAddressModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    Edit Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAddressForm">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> This will update your primary profile address. 
                        <a href="{{ route('customer.profile.edit') }}" class="alert-link">Go to Profile Settings</a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        Edit in Profile
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Address Form
    const addAddressForm = document.getElementById('addAddressForm');
    if (addAddressForm) {
        addAddressForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // For now, show a message that this feature is coming soon
            alert('Address book functionality is coming soon! For now, you can update your primary address in Profile Settings.');

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addAddressModal'));
            modal.hide();
        });
    }
});

function editAddress(type) {
    if (type === 'primary') {
        // Show edit modal or redirect to profile edit
        const editModal = new bootstrap.Modal(document.getElementById('editAddressModal'));
        editModal.show();
    }
}
</script>
@endpush
