@extends('layouts.frontend')

@section('title', 'Test Location Dropdown')

@section('content')
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Test Location Dropdown</h3>
                </div>
                <div class="card-body">
                    <form>
                        <h5>Bootstrap Style</h5>
                        <x-location-dropdown 
                            name="test_location_bootstrap"
                            style="bootstrap"
                        />
                        
                        <hr class="my-4">
                        
                        <h5>Tailwind Style</h5>
                        <x-location-dropdown 
                            name="test_location_tailwind"
                            style="tailwind"
                        />
                        
                        <hr class="my-4">
                        
                        <h5>With Initial Values</h5>
                        <x-location-dropdown 
                            name="test_location_initial"
                            style="bootstrap"
                            :country-value="1"
                            :state-value="1"
                            :city-value="1"
                        />
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary" onclick="getValues()">Get Values</button>
                            <button type="button" class="btn btn-secondary" onclick="resetDropdowns()">Reset</button>
                        </div>
                        
                        <div id="output" class="mt-3"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function getValues() {
    const containers = document.querySelectorAll('.location-dropdown-container');
    const output = document.getElementById('output');
    let html = '<h6>Current Values:</h6>';
    
    containers.forEach(container => {
        const name = container.getAttribute('data-name');
        const country = container.querySelector('.location-country-select')?.value || '';
        const state = container.querySelector('.location-state-select')?.value || '';
        const city = container.querySelector('.location-city-select')?.value || '';
        
        html += `<p><strong>${name}:</strong> Country: ${country}, State: ${state}, City: ${city}</p>`;
    });
    
    output.innerHTML = html;
}

function resetDropdowns() {
    const containers = document.querySelectorAll('.location-dropdown-container');
    containers.forEach(container => {
        const country = container.querySelector('.location-country-select');
        const state = container.querySelector('.location-state-select');
        const city = container.querySelector('.location-city-select');
        
        if (country) country.value = '';
        if (state) {
            state.innerHTML = '<option value="">Select State/Province</option>';
            state.disabled = true;
        }
        if (city) {
            city.innerHTML = '<option value="">Select City</option>';
            city.disabled = true;
        }
    });
    
    document.getElementById('output').innerHTML = '';
}
</script>
@endsection
