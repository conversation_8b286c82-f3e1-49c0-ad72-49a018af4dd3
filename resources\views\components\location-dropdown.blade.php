@props([
    'name' => 'location',
    'label' => 'Location',
    'required' => false,
    'placeholder' => 'Select...',
    'value' => null,
    'countryValue' => null,
    'stateValue' => null,
    'cityValue' => null,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'containerClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => 'text-red-500 text-sm mt-1',
    'loadingClass' => 'text-gray-500 text-sm',
    'searchable' => true,
    'clearable' => true,
    'disabled' => false,
    'readonly' => false,
    'id' => null,
    'style' => 'tailwind' // 'tailwind' or 'bootstrap'
])

@php
    $baseId = $id ?? $name;
    $countryId = $baseId . '_country';
    $stateId = $baseId . '_state';
    $cityId = $baseId . '_city';
    
    // Determine CSS classes based on style
    $selectClass = $style === 'bootstrap' 
        ? 'form-select' 
        : 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent';
    
    $labelBaseClass = $style === 'bootstrap'
        ? 'form-label'
        : 'block text-sm font-medium text-gray-700 mb-2';
        
    $containerBaseClass = $style === 'bootstrap'
        ? 'mb-3'
        : 'mb-4';
@endphp

<div class="location-dropdown-container {{ $containerClass }}" data-name="{{ $name }}">
    @if($showCountry)
    <div class="{{ $containerBaseClass }}">
        <label for="{{ $countryId }}" class="{{ $labelBaseClass }} {{ $labelClass }}">
            Country
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <select 
            name="{{ $name }}[country]" 
            id="{{ $countryId }}"
            class="location-country-select {{ $selectClass }} {{ $inputClass }}"
            data-target-state="{{ $stateId }}"
            data-target-city="{{ $cityId }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
        >
            <option value="">{{ $placeholder }}</option>
        </select>
        <div class="location-error country-error {{ $errorClass }}" style="display: none;"></div>
        <div class="location-loading country-loading {{ $loadingClass }}" style="display: none;">Loading countries...</div>
    </div>
    @endif

    @if($showState)
    <div class="{{ $containerBaseClass }}">
        <label for="{{ $stateId }}" class="{{ $labelBaseClass }} {{ $labelClass }}">
            State/Province
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <select 
            name="{{ $name }}[state]" 
            id="{{ $stateId }}"
            class="location-state-select {{ $selectClass }} {{ $inputClass }}"
            data-target-city="{{ $cityId }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
            disabled
        >
            <option value="">Select country first</option>
        </select>
        <div class="location-error state-error {{ $errorClass }}" style="display: none;"></div>
        <div class="location-loading state-loading {{ $loadingClass }}" style="display: none;">Loading states...</div>
    </div>
    @endif

    @if($showCity)
    <div class="{{ $containerBaseClass }}">
        <label for="{{ $cityId }}" class="{{ $labelBaseClass }} {{ $labelClass }}">
            City
            @if($required)<span class="text-red-500">*</span>@endif
        </label>
        <select 
            name="{{ $name }}[city]" 
            id="{{ $cityId }}"
            class="location-city-select {{ $selectClass }} {{ $inputClass }}"
            {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $readonly ? 'readonly' : '' }}
            disabled
        >
            <option value="">Select state first</option>
        </select>
        <div class="location-error city-error {{ $errorClass }}" style="display: none;"></div>
        <div class="location-loading city-loading {{ $loadingClass }}" style="display: none;">Loading cities...</div>
    </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('[data-name="{{ $name }}"]');

    if (container && window.LocationDropdown) {
        // Initialize the location dropdown
        window.LocationDropdown.init(container, {
            name: '{{ $name }}',
            searchable: {{ $searchable ? 'true' : 'false' }},
            clearable: {{ $clearable ? 'true' : 'false' }},
            initialValues: {
                country: '{{ $countryValue }}',
                state: '{{ $stateValue }}',
                city: '{{ $cityValue }}'
            },
            showCountry: {{ $showCountry ? 'true' : 'false' }},
            showState: {{ $showState ? 'true' : 'false' }},
            showCity: {{ $showCity ? 'true' : 'false' }},
            apiEndpoints: {
                countries: '/api/locations/countries',
                states: '/api/locations/countries/:country/states',
                cities: '/api/locations/states/:state/cities',
                search: '/api/locations/search'
            }
        });
    }
});
</script>
@endpush
