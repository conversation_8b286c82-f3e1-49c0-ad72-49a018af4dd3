<?php $__env->startSection('title', 'Apply for ' . $career->title . ' at ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('meta_description', 'Apply for the ' . $career->title . ' position at ' . ($siteSettings['site_name'] ?? 'Atrix Logistics') . '.'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }
    .step {
        display: flex;
        align-items: center;
        position: relative;
    }
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 0.5rem;
    }
    .step.active .step-number {
        background-color: #3b82f6;
        color: white;
    }
    .step.completed .step-number {
        background-color: #10b981;
        color: white;
    }
    .step.inactive .step-number {
        background-color: #e5e7eb;
        color: #6b7280;
    }
    .step-connector {
        width: 60px;
        height: 2px;
        background-color: #e5e7eb;
        margin: 0 1rem;
    }
    .step.completed + .step .step-connector {
        background-color: #10b981;
    }
    .form-step {
        display: none;
    }
    .form-step.active {
        display: block;
    }
    .file-upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
        transition: border-color 0.3s;
    }
    .file-upload-area:hover {
        border-color: #3b82f6;
    }
    .file-upload-area.dragover {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header with Breadcrumbs -->
    <?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => 'Apply for '.e($career->title).'','subtitle' => ''.e($career->department ? $career->department . ' • ' : '').''.e($career->location).'','description' => 'Complete the application form below to apply for this position. We\'ll review your application and get back to you soon.','breadcrumbs' => [
            ['title' => 'Home', 'url' => route('home')],
            ['title' => 'Careers', 'url' => route('careers.index')],
            ['title' => $career->title, 'url' => route('careers.show', $career->slug)],
            ['title' => 'Apply']
        ],'gradient' => 'from-blue-600 to-green-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Apply for '.e($career->title).'','subtitle' => ''.e($career->department ? $career->department . ' • ' : '').''.e($career->location).'','description' => 'Complete the application form below to apply for this position. We\'ll review your application and get back to you soon.','breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            ['title' => 'Home', 'url' => route('home')],
            ['title' => 'Careers', 'url' => route('careers.index')],
            ['title' => $career->title, 'url' => route('careers.show', $career->slug)],
            ['title' => 'Apply']
        ]),'gradient' => 'from-blue-600 to-green-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

    <!-- Application Form -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <!-- Step Indicator -->
                <div class="step-indicator mb-8">
                    <div class="step active" id="step-1-indicator">
                        <div class="step-number">1</div>
                        <span class="hidden sm:inline">Personal Info</span>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step inactive" id="step-2-indicator">
                        <div class="step-number">2</div>
                        <span class="hidden sm:inline">Experience</span>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step inactive" id="step-3-indicator">
                        <div class="step-number">3</div>
                        <span class="hidden sm:inline">Documents</span>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step inactive" id="step-4-indicator">
                        <div class="step-number">4</div>
                        <span class="hidden sm:inline">Review</span>
                    </div>
                </div>

                <!-- Form -->
                <form id="applicationForm" method="POST" action="<?php echo e(route('careers.apply.store', $career->slug)); ?>" enctype="multipart/form-data" class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                    <?php echo csrf_field(); ?>

                    <!-- Step 1: Personal Information -->
                    <div class="form-step active" id="step-1">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Personal Information</h2>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                <input type="text" id="first_name" name="first_name" required 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('first_name')); ?>">
                                <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" required 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('last_name')); ?>">
                                <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                <input type="email" id="email" name="email" required 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('email')); ?>">
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" required 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       value="<?php echo e(old('phone')); ?>">
                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <textarea id="address" name="address" rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Street address, city, state, postal code, country"><?php echo e(old('address')); ?></textarea>
                            <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Location Dropdown Component -->
                        <div class="mt-6">
                            <?php if (isset($component)) { $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.location-dropdown','data' => ['name' => 'location','style' => 'tailwind','countryValue' => old('location.country'),'stateValue' => old('location.state'),'cityValue' => old('location.city')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('location-dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'location','style' => 'tailwind','country-value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('location.country')),'state-value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('location.state')),'city-value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('location.city'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $attributes = $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $component = $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
                        </div>

                        <div class="flex justify-end mt-8">
                            <button type="button" onclick="nextStep()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Next Step
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Experience & Skills -->
                    <div class="form-step" id="step-2">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Experience & Skills</h2>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="years_of_experience" class="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
                                <select id="years_of_experience" name="years_of_experience" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Select experience level</option>
                                    <option value="0" <?php echo e(old('years_of_experience') == '0' ? 'selected' : ''); ?>>Entry Level (0-1 years)</option>
                                    <option value="2" <?php echo e(old('years_of_experience') == '2' ? 'selected' : ''); ?>>2-3 years</option>
                                    <option value="4" <?php echo e(old('years_of_experience') == '4' ? 'selected' : ''); ?>>4-5 years</option>
                                    <option value="6" <?php echo e(old('years_of_experience') == '6' ? 'selected' : ''); ?>>6-10 years</option>
                                    <option value="11" <?php echo e(old('years_of_experience') == '11' ? 'selected' : ''); ?>>10+ years</option>
                                </select>
                            </div>

                            <div>
                                <label for="expected_salary" class="block text-sm font-medium text-gray-700 mb-2">Expected Salary (Optional)</label>
                                <input type="number" id="expected_salary" name="expected_salary" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Annual salary expectation"
                                       value="<?php echo e(old('expected_salary')); ?>">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="availability" class="block text-sm font-medium text-gray-700 mb-2">Availability</label>
                                <select id="availability" name="availability" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Select availability</option>
                                    <option value="immediate" <?php echo e(old('availability') == 'immediate' ? 'selected' : ''); ?>>Immediate</option>
                                    <option value="2-weeks" <?php echo e(old('availability') == '2-weeks' ? 'selected' : ''); ?>>2 weeks notice</option>
                                    <option value="1-month" <?php echo e(old('availability') == '1-month' ? 'selected' : ''); ?>>1 month notice</option>
                                    <option value="2-months" <?php echo e(old('availability') == '2-months' ? 'selected' : ''); ?>>2+ months</option>
                                </select>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="willing_to_relocate" name="willing_to_relocate" value="1" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       <?php echo e(old('willing_to_relocate') ? 'checked' : ''); ?>>
                                <label for="willing_to_relocate" class="ml-2 block text-sm text-gray-700">
                                    Willing to relocate if required
                                </label>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label for="skills" class="block text-sm font-medium text-gray-700 mb-2">Skills (one per line)</label>
                            <textarea id="skills" name="skills_text" rows="4" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="List your relevant skills, one per line"><?php echo e(old('skills_text')); ?></textarea>
                            <p class="text-sm text-gray-500 mt-1">Enter each skill on a new line</p>
                        </div>

                        <div class="mt-6">
                            <label for="why_interested" class="block text-sm font-medium text-gray-700 mb-2">Why are you interested in this position?</label>
                            <textarea id="why_interested" name="why_interested" rows="4" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Tell us what motivates you to apply for this role..."><?php echo e(old('why_interested')); ?></textarea>
                        </div>

                        <div class="flex justify-between mt-8">
                            <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Previous
                            </button>
                            <button type="button" onclick="nextStep()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Next Step
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Documents -->
                    <div class="form-step" id="step-3">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Documents & Cover Letter</h2>
                        
                        <div class="mb-6">
                            <label for="cover_letter" class="block text-sm font-medium text-gray-700 mb-2">Cover Letter (Optional)</label>
                            <textarea id="cover_letter" name="cover_letter" rows="6" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Write a brief cover letter explaining your interest and qualifications..."><?php echo e(old('cover_letter')); ?></textarea>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Resume/CV (Optional)</label>
                            <div class="file-upload-area" id="resumeUploadArea">
                                <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx" class="hidden">
                                <div id="uploadText">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                    <p class="text-lg font-medium text-gray-700 mb-2">Upload your resume</p>
                                    <p class="text-sm text-gray-500 mb-4">Drag and drop your file here, or click to browse</p>
                                    <button type="button" onclick="document.getElementById('resume').click()" 
                                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium transition-colors">
                                        Choose File
                                    </button>
                                    <p class="text-xs text-gray-400 mt-2">Supported formats: PDF, DOC, DOCX (Max 5MB)</p>
                                </div>
                                <div id="fileInfo" class="hidden">
                                    <i class="fas fa-file-alt text-4xl text-green-500 mb-4"></i>
                                    <p class="text-lg font-medium text-gray-700 mb-2" id="fileName"></p>
                                    <button type="button" onclick="removeFile()" 
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded font-medium transition-colors">
                                        Remove File
                                    </button>
                                </div>
                            </div>
                            <?php $__errorArgs = ['resume'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-6">
                            <label for="referral_source" class="block text-sm font-medium text-gray-700 mb-2">How did you hear about this position?</label>
                            <select id="referral_source" name="referral_source" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select source</option>
                                <option value="company-website" <?php echo e(old('referral_source') == 'company-website' ? 'selected' : ''); ?>>Company Website</option>
                                <option value="job-board" <?php echo e(old('referral_source') == 'job-board' ? 'selected' : ''); ?>>Job Board</option>
                                <option value="social-media" <?php echo e(old('referral_source') == 'social-media' ? 'selected' : ''); ?>>Social Media</option>
                                <option value="referral" <?php echo e(old('referral_source') == 'referral' ? 'selected' : ''); ?>>Employee Referral</option>
                                <option value="recruiter" <?php echo e(old('referral_source') == 'recruiter' ? 'selected' : ''); ?>>Recruiter</option>
                                <option value="other" <?php echo e(old('referral_source') == 'other' ? 'selected' : ''); ?>>Other</option>
                            </select>
                        </div>

                        <div class="flex justify-between mt-8">
                            <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Previous
                            </button>
                            <button type="button" onclick="nextStep()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Review Application
                            </button>
                        </div>
                    </div>

                    <!-- Step 4: Review & Submit -->
                    <div class="form-step" id="step-4">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Review Your Application</h2>
                        
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Application Summary</h3>
                            <div id="reviewContent">
                                <!-- Content will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="mb-6">
                            <div class="flex items-start">
                                <input type="checkbox" id="consent_data_processing" name="consent_data_processing" value="1" required
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1">
                                <label for="consent_data_processing" class="ml-2 block text-sm text-gray-700">
                                    I consent to the processing of my personal data for recruitment purposes. I understand that my information will be used to evaluate my application and may be stored for future opportunities. *
                                </label>
                            </div>
                            <?php $__errorArgs = ['consent_data_processing'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="flex justify-between mt-8">
                            <button type="button" onclick="prevStep()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Previous
                            </button>
                            <button type="submit" id="submitBtn" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
                                <span id="submitText">Submit Application</span>
                                <span id="submitSpinner" class="hidden">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Submitting...
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
(function() {
    'use strict';

    let currentStep = 1;
    const totalSteps = 4;

    function updateStepIndicator() {
        for (let i = 1; i <= totalSteps; i++) {
            const indicator = document.getElementById(`step-${i}-indicator`);
            const step = document.getElementById(`step-${i}`);
            
            if (i < currentStep) {
                indicator.className = 'step completed';
                step.classList.remove('active');
            } else if (i === currentStep) {
                indicator.className = 'step active';
                step.classList.add('active');
            } else {
                indicator.className = 'step inactive';
                step.classList.remove('active');
            }
        }
    }

    function nextStep() {
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                updateStepIndicator();
                
                if (currentStep === 4) {
                    populateReview();
                }
            }
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepIndicator();
        }
    }

    function validateCurrentStep() {
        const currentStepElement = document.getElementById(`step-${currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');
                isValid = false;
            } else {
                field.classList.remove('border-red-500');
            }
        });

        if (!isValid) {
            alert('Please fill in all required fields before proceeding.');
        }

        return isValid;
    }

    function populateReview() {
        const reviewContent = document.getElementById('reviewContent');
        const formData = new FormData(document.getElementById('applicationForm'));
        
        let html = '<div class="space-y-4">';
        
        // Personal Information
        html += '<div><h4 class="font-semibold text-gray-900">Personal Information</h4>';
        html += `<p><strong>Name:</strong> ${formData.get('first_name')} ${formData.get('last_name')}</p>`;
        html += `<p><strong>Email:</strong> ${formData.get('email')}</p>`;
        html += `<p><strong>Phone:</strong> ${formData.get('phone')}</p>`;
        if (formData.get('address')) {
            html += `<p><strong>Address:</strong> ${formData.get('address')}</p>`;
        }
        html += '</div>';
        
        // Experience
        html += '<div><h4 class="font-semibold text-gray-900">Experience & Skills</h4>';
        if (formData.get('years_of_experience')) {
            const expText = document.querySelector(`option[value="${formData.get('years_of_experience')}"]`).textContent;
            html += `<p><strong>Experience:</strong> ${expText}</p>`;
        }
        if (formData.get('expected_salary')) {
            html += `<p><strong>Expected Salary:</strong> $${formData.get('expected_salary')}</p>`;
        }
        if (formData.get('availability')) {
            html += `<p><strong>Availability:</strong> ${formData.get('availability')}</p>`;
        }
        if (formData.get('willing_to_relocate')) {
            html += `<p><strong>Willing to relocate:</strong> Yes</p>`;
        }
        html += '</div>';
        
        // Documents
        html += '<div><h4 class="font-semibold text-gray-900">Documents</h4>';
        const resumeFile = document.getElementById('resume').files[0];
        if (resumeFile) {
            html += `<p><strong>Resume:</strong> ${resumeFile.name}</p>`;
        }
        if (formData.get('cover_letter')) {
            html += `<p><strong>Cover Letter:</strong> Provided</p>`;
        }
        html += '</div>';
        
        html += '</div>';
        reviewContent.innerHTML = html;
    }

    // File upload handling
    const resumeInput = document.getElementById('resume');
    const uploadArea = document.getElementById('resumeUploadArea');
    const uploadText = document.getElementById('uploadText');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');

    resumeInput.addEventListener('change', handleFileSelect);

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            resumeInput.files = files;
            handleFileSelect();
        }
    });

    function handleFileSelect() {
        const file = resumeInput.files[0];
        if (file) {
            fileName.textContent = file.name;
            uploadText.classList.add('hidden');
            fileInfo.classList.remove('hidden');
        }
    }

    function removeFile() {
        resumeInput.value = '';
        uploadText.classList.remove('hidden');
        fileInfo.classList.add('hidden');
    }

    // AJAX form submission
    document.getElementById('applicationForm').addEventListener('submit', function(e) {
        e.preventDefault();

        if (currentStep !== 4) {
            return;
        }

        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const submitSpinner = document.getElementById('submitSpinner');

        // Disable submit button and show loading
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        submitSpinner.classList.remove('hidden');

        const formData = new FormData(this);

        // Convert skills textarea to array
        const skillsText = document.querySelector('[name="skills_text"]').value;
        if (skillsText) {
            const skillsArray = skillsText.split('\n').filter(skill => skill.trim() !== '');
            skillsArray.forEach((skill, index) => {
                formData.append(`skills[${index}]`, skill.trim());
            });
        }

        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw response;
        })
        .then(data => {
            // Success
            showSuccessMessage(data.message || 'Your application has been submitted successfully!');

            // Reset form after delay
            setTimeout(() => {
                window.location.href = '<?php echo e(route("careers.index")); ?>';
            }, 3000);
        })
        .catch(async (error) => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitText.classList.remove('hidden');
            submitSpinner.classList.add('hidden');

            if (error.status === 422) {
                // Validation errors
                const errorData = await error.json();
                showValidationErrors(errorData.errors);
            } else if (error.status === 404) {
                showErrorMessage('This position is no longer available for applications.');
            } else {
                showErrorMessage('An error occurred while submitting your application. Please try again.');
            }
        });
    });

    function showSuccessMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50';
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-3"></i>
                <div>
                    <div class="font-semibold">Success!</div>
                    <div>${message}</div>
                </div>
            </div>
        `;
        document.body.appendChild(alertDiv);

        // Remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    function showErrorMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50';
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-3"></i>
                <div>
                    <div class="font-semibold">Error</div>
                    <div>${message}</div>
                </div>
            </div>
        `;
        document.body.appendChild(alertDiv);

        // Remove after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    function showValidationErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.border-red-500').forEach(el => {
            el.classList.remove('border-red-500');
        });
        document.querySelectorAll('.text-red-500').forEach(el => {
            if (el.classList.contains('error-message')) {
                el.remove();
            }
        });

        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('border-red-500');

                const errorDiv = document.createElement('div');
                errorDiv.className = 'text-red-500 text-sm mt-1 error-message';
                errorDiv.textContent = errors[field][0];
                input.parentNode.appendChild(errorDiv);
            }
        });

        // Go back to first step with errors
        const firstErrorField = Object.keys(errors)[0];
        const firstErrorInput = document.querySelector(`[name="${firstErrorField}"]`);
        if (firstErrorInput) {
            const stepElement = firstErrorInput.closest('.form-step');
            if (stepElement) {
                const stepNumber = parseInt(stepElement.id.replace('step-', ''));
                currentStep = stepNumber;
                updateStepIndicator();
                firstErrorInput.focus();
            }
        }

        showErrorMessage('Please correct the errors and try again.');
    }
})();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/careers/apply.blade.php ENDPATH**/ ?>