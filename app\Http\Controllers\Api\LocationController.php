<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Http\Request;

class LocationController extends Controller
{
    /**
     * Get all countries
     */
    public function countries()
    {
        try {
            $countries = Country::where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get(['id', 'name', 'code', 'iso2', 'phone_code', 'currency', 'flag']);

            return response()->json([
                'success' => true,
                'data' => $countries
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load countries'
            ], 500);
        }
    }

    /**
     * Get states by country
     */
    public function states($countryId)
    {
        try {
            $states = State::where('country_id', $countryId)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get(['id', 'name', 'code']);

            return response()->json([
                'success' => true,
                'data' => $states
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load states'
            ], 500);
        }
    }

    /**
     * Get cities by state
     */
    public function cities($stateId)
    {
        try {
            $cities = City::where('state_id', $stateId)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get(['id', 'name']);

            return response()->json([
                'success' => true,
                'data' => $cities
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load cities'
            ], 500);
        }
    }

    /**
     * Search locations
     */
    public function search(Request $request)
    {
        try {
            $query = $request->get('q', '');
            $type = $request->get('type', 'all'); // country, state, city, or all
            
            $results = [];
            
            if ($type === 'all' || $type === 'country') {
                $countries = Country::where('is_active', true)
                    ->where('name', 'LIKE', "%{$query}%")
                    ->limit(10)
                    ->get(['id', 'name', 'code']);
                    
                foreach ($countries as $country) {
                    $results[] = [
                        'id' => $country->id,
                        'name' => $country->name,
                        'type' => 'country',
                        'code' => $country->code
                    ];
                }
            }
            
            if ($type === 'all' || $type === 'state') {
                $states = State::with('country')
                    ->where('is_active', true)
                    ->where('name', 'LIKE', "%{$query}%")
                    ->limit(10)
                    ->get(['id', 'name', 'code', 'country_id']);
                    
                foreach ($states as $state) {
                    $results[] = [
                        'id' => $state->id,
                        'name' => $state->name,
                        'type' => 'state',
                        'code' => $state->code,
                        'country' => $state->country->name ?? ''
                    ];
                }
            }
            
            if ($type === 'all' || $type === 'city') {
                $cities = City::with(['state.country'])
                    ->where('is_active', true)
                    ->where('name', 'LIKE', "%{$query}%")
                    ->limit(10)
                    ->get(['id', 'name', 'state_id']);
                    
                foreach ($cities as $city) {
                    $results[] = [
                        'id' => $city->id,
                        'name' => $city->name,
                        'type' => 'city',
                        'state' => $city->state->name ?? '',
                        'country' => $city->state->country->name ?? ''
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed'
            ], 500);
        }
    }
}
