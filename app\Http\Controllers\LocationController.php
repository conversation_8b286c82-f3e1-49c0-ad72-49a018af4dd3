<?php

namespace App\Http\Controllers;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LocationController extends Controller
{
    /**
     * Get all countries
     */
    public function countries(Request $request): JsonResponse
    {
        $search = $request->get('search');

        $query = Country::active()->ordered();

        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $countries = $query->get(['id', 'name', 'code', 'iso2', 'phone_code', 'currency', 'flag']);

        return response()->json([
            'success' => true,
            'data' => $countries
        ]);
    }

    /**
     * Get states by country
     */
    public function states(Request $request, $countryId): JsonResponse
    {
        $search = $request->get('search');

        $country = Country::active()->find($countryId);

        if (!$country) {
            return response()->json([
                'success' => false,
                'message' => 'Country not found'
            ], 404);
        }

        $query = $country->activeStates();

        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $states = $query->get(['id', 'name', 'code']);

        return response()->json([
            'success' => true,
            'data' => $states
        ]);
    }

    /**
     * Get cities by state
     */
    public function cities(Request $request, $stateId): JsonResponse
    {
        $search = $request->get('search');

        $state = State::active()->find($stateId);

        if (!$state) {
            return response()->json([
                'success' => false,
                'message' => 'State not found'
            ], 404);
        }

        $query = $state->activeCities();

        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $cities = $query->get(['id', 'name']);

        return response()->json([
            'success' => true,
            'data' => $cities
        ]);
    }

    /**
     * Search locations (countries, states, cities)
     */
    public function search(Request $request): JsonResponse
    {
        $search = $request->get('search');
        $type = $request->get('type', 'all'); // all, country, state, city

        if (!$search) {
            return response()->json([
                'success' => false,
                'message' => 'Search term is required'
            ], 400);
        }

        $results = [];

        if ($type === 'all' || $type === 'country') {
            $countries = Country::active()
                ->where('name', 'like', '%' . $search . '%')
                ->ordered()
                ->limit(10)
                ->get(['id', 'name', 'code', 'iso2']);

            foreach ($countries as $country) {
                $results[] = [
                    'id' => $country->id,
                    'name' => $country->name,
                    'type' => 'country',
                    'code' => $country->code,
                    'iso2' => $country->iso2,
                ];
            }
        }

        if ($type === 'all' || $type === 'state') {
            $states = State::active()
                ->with('country:id,name,code')
                ->where('name', 'like', '%' . $search . '%')
                ->ordered()
                ->limit(10)
                ->get(['id', 'country_id', 'name', 'code']);

            foreach ($states as $state) {
                $results[] = [
                    'id' => $state->id,
                    'name' => $state->name,
                    'type' => 'state',
                    'code' => $state->code,
                    'country' => $state->country->name,
                    'country_id' => $state->country_id,
                ];
            }
        }

        if ($type === 'all' || $type === 'city') {
            $cities = City::active()
                ->with(['state:id,name,country_id', 'state.country:id,name'])
                ->where('name', 'like', '%' . $search . '%')
                ->ordered()
                ->limit(10)
                ->get(['id', 'state_id', 'name']);

            foreach ($cities as $city) {
                $results[] = [
                    'id' => $city->id,
                    'name' => $city->name,
                    'type' => 'city',
                    'state' => $city->state->name,
                    'state_id' => $city->state_id,
                    'country' => $city->state->country->name,
                    'country_id' => $city->state->country_id,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $results
        ]);
    }
}
