<?php

namespace App\Http\Controllers;

use App\Models\Career;
use App\Models\JobApplication;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;

class CareerController extends Controller
{
    /**
     * Display a listing of careers
     */
    public function index(): View
    {
        $careers = Career::active()
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $departments = Career::active()
            ->whereNotNull('department')
            ->distinct()
            ->pluck('department')
            ->sort();

        return view('careers.index', compact('careers', 'departments'));
    }

    /**
     * Display the specified career
     */
    public function show(Career $career): View
    {
        if (!$career->is_active) {
            abort(404);
        }

        return view('careers.show', compact('career'));
    }

    /**
     * Show the job application form
     */
    public function apply(Career $career): View
    {
        if (!$career->is_active || $career->isApplicationDeadlinePassed()) {
            abort(404);
        }

        return view('careers.apply', compact('career'));
    }

    /**
     * Store a job application
     */
    public function storeApplication(Request $request, Career $career): RedirectResponse
    {
        if (!$career->is_active || $career->isApplicationDeadlinePassed()) {
            abort(404);
        }

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'nullable|string|max:500',
            'location.country' => 'nullable|integer|exists:countries,id',
            'location.state' => 'nullable|integer|exists:states,id',
            'location.city' => 'nullable|integer|exists:cities,id',
            'postal_code' => 'nullable|string|max:20',
            'cover_letter' => 'nullable|string|max:2000',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'years_of_experience' => 'nullable|integer|min:0|max:50',
            'expected_salary' => 'nullable|numeric|min:0',
            'availability' => 'nullable|string|max:255',
            'willing_to_relocate' => 'boolean',
            'skills' => 'nullable|array',
            'skills.*' => 'string|max:255',
            'why_interested' => 'nullable|string|max:1000',
            'referral_source' => 'nullable|string|max:255',
            'consent_data_processing' => 'required|accepted',
        ]);

        $validated['career_id'] = $career->id;
        $validated['willing_to_relocate'] = $request->has('willing_to_relocate');

        // Get location names from IDs
        $countryName = null;
        $stateName = null;
        $cityName = null;

        if (!empty($validated['location']['country'])) {
            $country = \App\Models\Country::find($validated['location']['country']);
            $countryName = $country ? $country->name : null;
        }

        if (!empty($validated['location']['state'])) {
            $state = \App\Models\State::find($validated['location']['state']);
            $stateName = $state ? $state->name : null;
        }

        if (!empty($validated['location']['city'])) {
            $city = \App\Models\City::find($validated['location']['city']);
            $cityName = $city ? $city->name : null;
        }

        // Remove location array and add individual fields
        unset($validated['location']);
        $validated['country'] = $countryName;
        $validated['state'] = $stateName;
        $validated['city'] = $cityName;

        // Handle resume upload
        if ($request->hasFile('resume')) {
            $validated['resume_path'] = $request->file('resume')->store('uploads/resumes', 'public');
        }

        JobApplication::create($validated);

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Your application has been submitted successfully! We will review it and get back to you soon.'
            ]);
        }

        return redirect()->route('careers.index')
                        ->with('success', 'Your application has been submitted successfully! We will review it and get back to you soon.');
    }

    /**
     * Filter careers by department
     */
    public function filterByDepartment(Request $request): View
    {
        $department = $request->get('department');

        $careers = Career::active()
            ->when($department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $departments = Career::active()
            ->whereNotNull('department')
            ->distinct()
            ->pluck('department')
            ->sort();

        return view('careers.index', compact('careers', 'departments', 'department'));
    }
}
